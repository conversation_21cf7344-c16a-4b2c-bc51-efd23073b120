<?php

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component {
    #[Validate('required|string|max:255|regex:/^[a-zA-Z\s\-\'\.]+$/')]
    public string $first_name = '';

    #[Validate('required|string|max:255|regex:/^[a-zA-Z\s\-\'\.]+$/')]
    public string $last_name = '';

    public string $username = '';

    #[Validate('required|string|lowercase|email:rfc,dns|max:255')]
    public string $email = '';

    #[Validate('required|string|confirmed')]
    public string $password = '';

    public string $password_confirmation = '';

    #[Validate('required|accepted')]
    public bool $terms_accepted = false;

    /**
     * Get validation rules
     */
    public function rules(): array
    {
        return [
            'first_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'last_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'username' => [
                'required',
                'string',
                'min:3',
                'max:30',
                'regex:/^[a-zA-Z0-9_]+$/',
                'unique:' . User::class,
            ],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email:rfc',
                'max:255',
                'unique:' . User::class,
            ],
            'password' => [
                'required',
                'string',
                'confirmed',
                Rules\Password::defaults(),
            ],
            'terms_accepted' => [
                'required',
                'accepted',
            ],
        ];
    }



    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $validated = $this->validate();

        // Hash the password
        $validated['password'] = Hash::make($validated['password']);

        // Set terms acceptance timestamp
        $validated['terms_accepted_at'] = now();

        // Remove terms_accepted from user data
        unset($validated['terms_accepted']);

        event(new Registered(($user = User::create($validated))));

        Auth::login($user);

        $this->redirectIntended(route('dashboard', absolute: false), navigate: true);
    }
}; ?>

<div class="flex flex-col gap-6">
    <x-auth-header
        :title="__('Join :platform', ['platform' => config('app.name', 'Graf')])"
        :description="__('Create your account to start analyzing YouTube data')"
    />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form method="POST" wire:submit="register" class="flex flex-col gap-6">
        <!-- Name Fields Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- First Name -->
            <flux:input
                wire:model.blur="first_name"
                :label="__('First name')"
                type="text"
                required
                autofocus
                autocomplete="given-name"
                :placeholder="__('First name')"
            />

            <!-- Last Name -->
            <flux:input
                wire:model.blur="last_name"
                :label="__('Last name')"
                type="text"
                required
                autocomplete="family-name"
                :placeholder="__('Last name')"
            />
        </div>

        <!-- Username -->
        <flux:input
            wire:model.blur="username"
            :label="__('Username')"
            type="text"
            required
            autocomplete="username"
            :placeholder="__('Choose a unique username')"
        />

        <!-- Email Address -->
        <flux:input
            wire:model.blur="email"
            :label="__('Email address')"
            type="email"
            required
            autocomplete="email"
            placeholder="<EMAIL>"
            :description="__('We\'ll use this to send you important updates')"
        />

        <!-- Password Fields Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Password -->
            <flux:input
                wire:model="password"
                :label="__('Password')"
                type="password"
                required
                autocomplete="new-password"
                :placeholder="__('Create a strong password')"
                viewable
            />

            <!-- Confirm Password -->
            <flux:input
                wire:model="password_confirmation"
                :label="__('Confirm password')"
                type="password"
                required
                autocomplete="new-password"
                :placeholder="__('Confirm your password')"
                viewable
            />
        </div>



        <!-- Terms Acceptance -->
        <div class="space-y-3">
            <flux:checkbox
                wire:model="terms_accepted"
                :label="__('I agree to the Terms of Service and Privacy Policy')"
                required
            />
            @error('terms_accepted')
                <flux:error>{{ !! $message }}</flux:error>
            @enderror
        </div>

        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" class="w-full">
                {{ __('Create account') }}
            </flux:button>
        </div>
    </form>

    <div class="space-x-1 rtl:space-x-reverse text-center text-sm text-zinc-600 dark:text-zinc-400">
        <span>{{ __('Already have an account?') }}</span>
        <flux:link :href="route('login')" wire:navigate>{{ __('Sign in') }}</flux:link>
    </div>
</div>
