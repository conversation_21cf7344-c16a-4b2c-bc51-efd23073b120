<?php

use <PERSON><PERSON><PERSON>\Youtube\Facades\Youtube;
use Alaouy\Youtube\Rules\ValidYoutubeVideo;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new class extends Component {
    #[Validate(['required', 'string', 'url'])]
    public string $youtube_url = '';
    
    public $videoData = null;
    public $error = null;
    public $loading = false;

    public function rules()
    {
        return [
            'youtube_url' => ['required', 'string', new ValidYoutubeVideo]
        ];
    }

    public function fetchVideoData()
    {
        $this->loading = true;
        $this->error = null;
        $this->videoData = null;

        try {
            $this->validate();
            
            // Extract video ID from URL
            $videoId = Youtube::parseVidFromURL($this->youtube_url);
            
            if (!$videoId) {
                throw new \Exception('Could not extract video ID from URL');
            }

            // Fetch video data
            $video = Youtube::getVideoInfo($videoId);
            
            if (!$video) {
                throw new \Exception('Video not found or API error');
            }

            $this->videoData = $video;
            
            // Debug dump the response
            dd([
                'success' => true,
                'video_id' => $videoId,
                'video_data' => $video,
                'url' => $this->youtube_url
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->error = 'Invalid YouTube URL provided.';
            dd([
                'success' => false,
                'error' => 'Validation failed',
                'validation_errors' => $e->errors(),
                'url' => $this->youtube_url
            ]);
        } catch (\Exception $e) {
            $this->error = 'Error fetching video data: ' . $e->getMessage();
            dd([
                'success' => false,
                'error' => $e->getMessage(),
                'url' => $this->youtube_url,
                'trace' => $e->getTraceAsString()
            ]);
        } finally {
            $this->loading = false;
        }
    }

    public function clearForm()
    {
        $this->youtube_url = '';
        $this->videoData = null;
        $this->error = null;
        $this->resetValidation();
    }
}; ?>

<div class="max-w-2xl mx-auto p-6">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">YouTube Video Data Fetcher</h2>
        
        <form wire:submit="fetchVideoData" class="space-y-4">
            <div>
                <flux:field>
                    <flux:label for="youtube_url">YouTube Video URL</flux:label>
                    <flux:input 
                        wire:model="youtube_url" 
                        id="youtube_url"
                        type="url" 
                        placeholder="https://www.youtube.com/watch?v=..." 
                        required
                    />
                    <flux:error name="youtube_url" />
                </flux:field>
            </div>

            <div class="flex gap-3">
                <flux:button 
                    type="submit" 
                    variant="primary"
                    :disabled="$loading"
                    class="flex items-center gap-2"
                >
                    @if($loading)
                        <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Fetching...
                    @else
                        Fetch Video Data
                    @endif
                </flux:button>

                @if($youtube_url || $videoData || $error)
                    <flux:button 
                        type="button" 
                        variant="ghost"
                        wire:click="clearForm"
                    >
                        Clear
                    </flux:button>
                @endif
            </div>
        </form>

        @if($error)
            <div class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            {{ $error }}
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if($videoData)
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Video Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The video data has been fetched and displayed in the debug output above.
                </p>
            </div>
        @endif
    </div>
</div>
