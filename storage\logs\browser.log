[2025-09-23 06:08:14] local.WARNING: Alpine Expression Error: Malformed arrow function parameter list

Expression: "__('I agree to the :terms and :privacy', [
                    'terms' => '<a href=" null null null null __('I agree to the :terms and :privacy', [
                    'terms' => '<a href= false 66 null null null null   false false  {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:08:14.471Z"} 
[2025-09-23 06:08:14] local.ERROR: Uncaught SyntaxError: Malformed arrow function parameter list https://graf.test/livewire/livewire.js?id=df3a17f2 1127 7 SyntaxError Malformed arrow function parameter list SyntaxError: Malformed arrow function parameter list
    at new AsyncFunction (<anonymous>)
    at safeAsyncFunction (https://graf.test/livewire/livewire.js?id=df3a17f2:1174:21)
    at generateFunctionFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1184:16)
    at generateEvaluatorFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1189:16)
    at normalEvaluator (https://graf.test/livewire/livewire.js?id=df3a17f2:1154:111)
    at evaluateLater (https://graf.test/livewire/livewire.js?id=df3a17f2:1144:12)
    at handler2 (https://graf.test/livewire/livewire.js?id=df3a17f2:3532:22)
    at flushHandlers (https://graf.test/livewire/livewire.js?id=df3a17f2:1281:48)
    at stopDeferring (https://graf.test/livewire/livewire.js?id=df3a17f2:1286:7)
    at deferHandlingDirectives (https://graf.test/livewire/livewire.js?id=df3a17f2:1289:5) {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:08:14.474Z"} 
[2025-09-23 06:08:14] local.ERROR: Uncaught SyntaxError: Malformed arrow function parameter list https://graf.test/livewire/livewire.js?id=df3a17f2 1127 7 SyntaxError Malformed arrow function parameter list SyntaxError: Malformed arrow function parameter list
    at new AsyncFunction (<anonymous>)
    at safeAsyncFunction (https://graf.test/livewire/livewire.js?id=df3a17f2:1174:21)
    at generateFunctionFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1184:16)
    at generateEvaluatorFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1189:16)
    at normalEvaluator (https://graf.test/livewire/livewire.js?id=df3a17f2:1154:111)
    at evaluateLater (https://graf.test/livewire/livewire.js?id=df3a17f2:1144:12)
    at handler2 (https://graf.test/livewire/livewire.js?id=df3a17f2:3532:22)
    at flushHandlers (https://graf.test/livewire/livewire.js?id=df3a17f2:1281:48)
    at stopDeferring (https://graf.test/livewire/livewire.js?id=df3a17f2:1286:7)
    at deferHandlingDirectives (https://graf.test/livewire/livewire.js?id=df3a17f2:1289:5) {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:08:14.474Z"} 
[2025-09-23 06:09:01] local.WARNING: Alpine Expression Error: Malformed arrow function parameter list

Expression: "__('I agree to the :terms and :privacy', [
                    'terms' => '<a href=" null null null null __('I agree to the :terms and :privacy', [
                    'terms' => '<a href= false 66 null null null null   false false  {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:09:01.823Z"} 
[2025-09-23 06:09:01] local.ERROR: Uncaught SyntaxError: Malformed arrow function parameter list https://graf.test/livewire/livewire.js?id=df3a17f2 1127 7 SyntaxError Malformed arrow function parameter list SyntaxError: Malformed arrow function parameter list
    at new AsyncFunction (<anonymous>)
    at safeAsyncFunction (https://graf.test/livewire/livewire.js?id=df3a17f2:1174:21)
    at generateFunctionFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1184:16)
    at generateEvaluatorFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1189:16)
    at normalEvaluator (https://graf.test/livewire/livewire.js?id=df3a17f2:1154:111)
    at evaluateLater (https://graf.test/livewire/livewire.js?id=df3a17f2:1144:12)
    at handler2 (https://graf.test/livewire/livewire.js?id=df3a17f2:3532:22)
    at flushHandlers (https://graf.test/livewire/livewire.js?id=df3a17f2:1281:48)
    at stopDeferring (https://graf.test/livewire/livewire.js?id=df3a17f2:1286:7)
    at deferHandlingDirectives (https://graf.test/livewire/livewire.js?id=df3a17f2:1289:5) {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:09:01.851Z"} 
[2025-09-23 06:09:01] local.ERROR: Uncaught SyntaxError: Malformed arrow function parameter list https://graf.test/livewire/livewire.js?id=df3a17f2 1127 7 SyntaxError Malformed arrow function parameter list SyntaxError: Malformed arrow function parameter list
    at new AsyncFunction (<anonymous>)
    at safeAsyncFunction (https://graf.test/livewire/livewire.js?id=df3a17f2:1174:21)
    at generateFunctionFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1184:16)
    at generateEvaluatorFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1189:16)
    at normalEvaluator (https://graf.test/livewire/livewire.js?id=df3a17f2:1154:111)
    at evaluateLater (https://graf.test/livewire/livewire.js?id=df3a17f2:1144:12)
    at handler2 (https://graf.test/livewire/livewire.js?id=df3a17f2:3532:22)
    at flushHandlers (https://graf.test/livewire/livewire.js?id=df3a17f2:1281:48)
    at stopDeferring (https://graf.test/livewire/livewire.js?id=df3a17f2:1286:7)
    at deferHandlingDirectives (https://graf.test/livewire/livewire.js?id=df3a17f2:1289:5) {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:09:01.851Z"} 
[2025-09-23 06:11:13] local.WARNING: Alpine Expression Error: Invalid or unexpected token

Expression: "__('I agree to the <a href=" null null null null __('I agree to the <a href= false 66 null null null null   false false  {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:11:13.319Z"} 
[2025-09-23 06:11:13] local.ERROR: Uncaught SyntaxError: Invalid or unexpected token https://graf.test/livewire/livewire.js?id=df3a17f2 1127 7 SyntaxError Invalid or unexpected token SyntaxError: Invalid or unexpected token
    at new AsyncFunction (<anonymous>)
    at safeAsyncFunction (https://graf.test/livewire/livewire.js?id=df3a17f2:1174:21)
    at generateFunctionFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1184:16)
    at generateEvaluatorFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1189:16)
    at normalEvaluator (https://graf.test/livewire/livewire.js?id=df3a17f2:1154:111)
    at evaluateLater (https://graf.test/livewire/livewire.js?id=df3a17f2:1144:12)
    at handler2 (https://graf.test/livewire/livewire.js?id=df3a17f2:3532:22)
    at flushHandlers (https://graf.test/livewire/livewire.js?id=df3a17f2:1281:48)
    at stopDeferring (https://graf.test/livewire/livewire.js?id=df3a17f2:1286:7)
    at deferHandlingDirectives (https://graf.test/livewire/livewire.js?id=df3a17f2:1289:5) {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:11:13.324Z"} 
[2025-09-23 06:11:13] local.ERROR: Uncaught SyntaxError: Invalid or unexpected token https://graf.test/livewire/livewire.js?id=df3a17f2 1127 7 SyntaxError Invalid or unexpected token SyntaxError: Invalid or unexpected token
    at new AsyncFunction (<anonymous>)
    at safeAsyncFunction (https://graf.test/livewire/livewire.js?id=df3a17f2:1174:21)
    at generateFunctionFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1184:16)
    at generateEvaluatorFromString (https://graf.test/livewire/livewire.js?id=df3a17f2:1189:16)
    at normalEvaluator (https://graf.test/livewire/livewire.js?id=df3a17f2:1154:111)
    at evaluateLater (https://graf.test/livewire/livewire.js?id=df3a17f2:1144:12)
    at handler2 (https://graf.test/livewire/livewire.js?id=df3a17f2:3532:22)
    at flushHandlers (https://graf.test/livewire/livewire.js?id=df3a17f2:1281:48)
    at stopDeferring (https://graf.test/livewire/livewire.js?id=df3a17f2:1286:7)
    at deferHandlingDirectives (https://graf.test/livewire/livewire.js?id=df3a17f2:1289:5) {"url":"https://graf.test/register","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36","timestamp":"2025-09-23T06:11:13.324Z"} 
