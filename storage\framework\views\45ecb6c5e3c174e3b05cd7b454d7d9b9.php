<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split($name, $params);

$__html = app('livewire')->mount($__name, $__params, 'lw-1184965681-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?><?php /**PATH C:\Users\<USER>\Herd\graf\storage\framework\views/a168b0ebcff266df6b662fc4b6d06e88.blade.php ENDPATH**/ ?>