---
type: "always_apply"
---

Here are all the tools available inside the `laravel mcp server`:
- `application-info`
Get comprehensive application information including PHP version, Laravel version, database engine, all installed packages with their versions, and all Eloquent models in the application. You should use this tool on each new chat, and use the package & version data to write version specific code for the packages that exist.
- `browser-logs`
Read the last N log entries from the BROWSER log. Very helpful for debugging the frontend and JS/Javascript
- `database-connections`
List the configured database connection names for this application.
- `database-query`
Execute a read-only SQL query against the configured database.
- `database-schema`
Read the database schema for this application, including table names, columns, data types, indexes, foreign keys, and more.
- `get-absolute-url`
Get the absolute URL for a given relative path or named route. If no arguments are provided, you will get the absolute URL for "/"
- `get-config`
Get the value of a specific config variable using dot notation (e.g., "app.name", "database.default")
- `last-error`
Get details of the last error/exception created in this application on the backend. Use browser-log tool for browser errors.
- `list-artisan-commands`
List all available Artisan commands registered in this application.
- `list-available-config-keys`
List all available Laravel configuration keys (from config/*.php) in dot notation.
- `list-available-env-vars`
🔧 List all available environment variable names from a given .env file (default .env).
- `list-routes`
List all available routes defined in the application, including Folio routes if used
- `read-log-entries`
Read the last N log entries from the application log, correctly handling multi-line PSR-3 formatted logs. Only works for log files.
- `report-feedback`
Report feedback from the user on what would make Boost, or their experience with Laravel, better. Ask the user for more details before use if ambiguous or unclear. This is only for feedback related to Boost or the Laravel ecosystem. Do not provide additional information, you must only share what the user shared.
- `search-docs`
Search for up-to-date version-specific documentation related to this project and its packages. This tool will search Laravel hosted documentation based on the packages installed and is perfect for all Laravel ecosystem packages. Laravel, Inertia, Pest, Livewire, Filament, Nova, Tailwind, and more. You must use this tool to search for Laravel-ecosystem docs before using other approaches. The results provided are for this project's package version and does not cover all versions of the package.
- `tinker`
Execute PHP code in the Laravel application context, like artisan tinker. Use this for debugging issues, checking if functions exist, and testing code snippets. You should not create models directly without explicit user approval. Prefer Unit/Feature tests using factories for functionality testing. Prefer existing artisan commands over custom tinker code. Returns the output of the code, as well as whatever is "returned" using "return".
-----------------
Use them extensively. Rely on them + the artisan commands! I do not care about your assumptions.
IMPORTANTLY: STRICTLY adhre to LARAVEL >= 12.x, no older concepts is accepts.
Be patient, and use tools in between responses and tasks!