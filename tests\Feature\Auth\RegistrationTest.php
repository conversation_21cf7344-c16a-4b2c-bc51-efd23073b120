<?php

use App\Models\User;
use Livewire\Volt\Volt;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('registration screen can be rendered', function () {
    $this->get(route('register'))
        ->assertStatus(200);
});

test('new users can register', function () {
    $response = Volt::test('auth.register')
        ->set('first_name', 'Test')
        ->set('last_name', 'User')
        ->set('username', 'testuser')
        ->set('email', '<EMAIL>')
        ->set('password', 'password')
        ->set('password_confirmation', 'password')
        ->set('terms_accepted', true)
        ->call('register');

    if ($response->errors()->isNotEmpty()) {
        dump('Validation errors:', $response->errors()->toArray());
    }

    $response
        ->assertHasNoErrors()
        ->assertRedirect(route('dashboard', absolute: false));

    $this->assertAuthenticated();

    // Verify user was created with correct data
    $this->assertDatabaseHas('users', [
        'first_name' => 'Test',
        'last_name' => 'User',
        'username' => 'testuser',
        'email' => '<EMAIL>',
    ]);
});

test('registration requires all fields', function () {
    $response = Volt::test('auth.register')
        ->call('register');

    $response->assertHasErrors([
        'first_name',
        'last_name',
        'username',
        'email',
        'password',
        'terms_accepted',
    ]);
});

test('registration validates username format', function () {
    Volt::test('auth.register')
        ->set('username', 'invalid username!')
        ->call('register')
        ->assertHasErrors(['username']);

    Volt::test('auth.register')
        ->set('username', 'ab') // Too short
        ->call('register')
        ->assertHasErrors(['username']);
});

test('registration validates email uniqueness', function () {
    User::factory()->create(['email' => '<EMAIL>']);

    Volt::test('auth.register')
        ->set('first_name', 'Test')
        ->set('last_name', 'User')
        ->set('username', 'testuser')
        ->set('email', '<EMAIL>')
        ->set('password', 'password')
        ->set('password_confirmation', 'password')
        ->set('terms_accepted', true)
        ->call('register')
        ->assertHasErrors(['email']);
});

test('registration validates username uniqueness', function () {
    User::factory()->create(['username' => 'testuser']);

    Volt::test('auth.register')
        ->set('first_name', 'Test')
        ->set('last_name', 'User')
        ->set('username', 'testuser')
        ->set('email', '<EMAIL>')
        ->set('password', 'password')
        ->set('password_confirmation', 'password')
        ->set('terms_accepted', true)
        ->call('register')
        ->assertHasErrors(['username']);
});

test('registration requires terms acceptance', function () {
    Volt::test('auth.register')
        ->set('first_name', 'Test')
        ->set('last_name', 'User')
        ->set('username', 'testuser')
        ->set('email', '<EMAIL>')
        ->set('password', 'password')
        ->set('password_confirmation', 'password')
        ->set('terms_accepted', false)
        ->call('register')
        ->assertHasErrors(['terms_accepted']);
});