@php
use <PERSON><PERSON>uy\Youtube\Facades\Youtube;
use Alaouy\Youtube\Rules\ValidYoutubeVideo;

$youtube_url = request('youtube_url', '');
$channel_input = request('channel_input', '');
$videoData = null;
$channelData = null;

if (request()->isMethod('post') && $youtube_url) {
    try {
        // Validate the URL
        $validator = validator(['youtube_url' => $youtube_url], [
            'youtube_url' => ['bail', 'required', 'string', new ValidYoutubeVideo]
        ]);

        if (!$validator->fails()) {
            // Extract video ID from URL
            $videoId = Youtube::parseVidFromURL($youtube_url);

            if (!$videoId) {
                throw new \Exception('Could not extract video ID from URL');
            }

            // Fetch video data
            $video = Youtube::getVideoInfo($videoId);

            dd($video);
        }
    } catch (\Exception $e) {
        throw new \Exception('Error fetching video data: ' . $e->getMessage());
    }
}

if (request()->isMethod('post') && $channel_input) {
    try {
        // Check if it is a handle url (/@...): if so use getChannelByHandle
        if (str_contains($channel_input, '/@')) {
            $handle = str_replace(['https://www.youtube.com/@', 'http://www.youtube.com/@'], '', $channel_input);
            $channel = Youtube::getChannelByHandle($handle);
        } else {
            // Else: use getChannelById
            $channel = Youtube::getChannelById($channel_input);
        }

        dd($channel);
    } catch (\Exception $e) {
        throw new \Exception('Error fetching channel data: ' . $e->getMessage());
    }
}
@endphp

<div class="max-w-2xl mx-auto p-6">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">YouTube Video Data Fetcher</h2>
        
        <form method="POST" action="{{ route('dashboard') }}" class="space-y-6">
            @csrf

            <!-- Video URL Section -->
            <div>
                <label for="youtube_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    YouTube Video URL
                </label>
                <input
                    name="youtube_url"
                    id="youtube_url"
                    type="url"
                    placeholder="https://www.youtube.com/watch?v=..."
                    value="{{ old('youtube_url', $youtube_url) }}"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                @if(session('validation_errors') && in_array('youtube_url', array_keys(session('validation_errors', []))))
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">
                        {{ session('validation_errors')['youtube_url'][0] ?? 'Invalid YouTube URL' }}
                    </p>
                @endif
                <div class="mt-2">
                    <flux:button
                        type="submit"
                        variant="primary"
                    >
                        Fetch Video Data
                    </flux:button>
                </div>
            </div>

            <!-- Channel Input Section -->
            <div>
                <label for="channel_input" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    YouTube Channel (ID, URL, or Handle)
                </label>
                <input
                    name="channel_input"
                    id="channel_input"
                    type="text"
                    placeholder="UCk1SpWNzOs4MYmr0uICEntg or @google or https://www.youtube.com/@google"
                    value="{{ old('channel_input', $channel_input) }}"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                />
                <div class="mt-2">
                    <flux:button
                        type="submit"
                        variant="primary"
                    >
                        Fetch Channel Data
                    </flux:button>
                </div>
            </div>

            @if($youtube_url || $channel_input)
                <div>
                    <flux:button
                        type="button"
                        variant="ghost"
                        onclick="window.location.href='{{ route('dashboard') }}'"
                    >
                        Clear All
                    </flux:button>
                </div>
            @endif
        </form>


        @if($videoData)
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Video Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The video data has been fetched and displayed in the debug output above.
                </p>
            </div>
        @endif

        @if($channelData)
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Channel Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The channel data has been fetched and displayed in the debug output above.
                </p>
            </div>
        @endif
    </div>
</div>
