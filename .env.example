APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
# Platform Configuration
APP_DESCRIPTION="The easy youtube anaylyzer. Focus on videos and comments processing."
APP_DESCRIPTION="YouTube Analytics and Insights Platform"
APP_TAGLINE="Unlock the power of YouTube data"

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Registration Settings
REGISTRATION_ENABLED=true
REGISTRATION_REQUIRES_EMAIL_VERIFICATION=true
REGISTRATION_REQUIRES_TERMS=true
REGISTRATION_REQUIRES_CAPTCHA=true
USERNAME_MIN_LENGTH=3
USERNAME_MAX_LENGTH=30

# Captcha Configuration
CAPTCHA_PRIMARY_PROVIDER=turnstile
CAPTCHA_FALLBACK_PROVIDER=recaptcha

# Cloudflare Turnstile
TURNSTILE_ENABLED=true
TURNSTILE_SITE_KEY=
TURNSTILE_SECRET_KEY=

# Google reCAPTCHA
RECAPTCHA_ENABLED=true
RECAPTCHA_SITE_KEY=
RECAPTCHA_SECRET_KEY=
RECAPTCHA_VERSION=v2

# Legal Pages
TERMS_OF_SERVICE_URL="/terms"
PRIVACY_POLICY_URL="/privacy"
COOKIE_POLICY_URL="/cookies"
DATA_DELETION_URL="/data-deletion"

# Data Retention
USER_DATA_RETENTION_DAYS=2555
ANALYTICS_DATA_RETENTION_DAYS=1095
ALLOW_DATA_DELETION_REQUESTS=true

# YouTube API (already exists but documenting here)
YOUTUBE_API_KEY=

# Future Platform APIs
TWITCH_API_KEY=
TIKTOK_API_KEY=
