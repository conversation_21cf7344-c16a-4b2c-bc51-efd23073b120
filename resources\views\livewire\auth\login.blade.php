<?php

use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component {
    #[Validate('required|string|email')]
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->ensureIsNotRateLimited();

        if (! Auth::attempt(['email' => $this->email, 'password' => $this->password], $this->remember)) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => __('auth.failed'),
            ]);
        }

        RateLimiter::clear($this->throttleKey());
        Session::regenerate();

        $this->redirectIntended(default: route('dashboard', absolute: false), navigate: true);
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email).'|'.request()->ip());
    }
}; ?>

<div class="flex flex-col gap-6">
    <x-auth-header
        :title="__('Welcome back to :platform', ['platform' => config('app.name', 'Graf')])"
        :description="__('Sign in to your account to continue analyzing YouTube data')"
    />

    <!-- Session Status -->
    <x-auth-session-status class="text-center" :status="session('status')" />

    <form method="POST" wire:submit="login" class="flex flex-col gap-6">
        <!-- Email Address -->
        <flux:input
            wire:model.blur="email"
            :label="__('Email address')"
            type="email"
            required
            autofocus
            autocomplete="email"
            placeholder="<EMAIL>"
            :description="__('Enter the email address associated with your account')"
        />

        <!-- Password -->
        <div class="space-y-2">
            <div class="flex items-center justify-between">
                <flux:label for="password">{{ __('Password') }}</flux:label>
                @if (Route::has('password.request'))
                    <flux:link
                        class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        :href="route('password.request')"
                        wire:navigate
                    >
                        {{ __('Forgot password?') }}
                    </flux:link>
                @endif
            </div>
            <flux:input
                wire:model="password"
                id="password"
                type="password"
                required
                autocomplete="current-password"
                :placeholder="__('Enter your password')"
                viewable
            />
        </div>

        <!-- Remember Me -->
        <div class="flex items-center justify-between">
            <flux:checkbox
                wire:model="remember"
                :label="__('Keep me signed in')"
                :description="__('For up to 30 days on this device')"
            />
        </div>

        <div class="flex items-center justify-end">
            <flux:button variant="primary" type="submit" class="w-full">
                {{ __('Sign in') }}
            </flux:button>
        </div>
    </form>

    @if (Route::has('register'))
        <div class="space-x-1 rtl:space-x-reverse text-center text-sm text-zinc-600 dark:text-zinc-400">
            <span>{{ __('New to :platform?', ['platform' => config('app.name', 'Graf')]) }}</span>
            <flux:link :href="route('register')" wire:navigate>{{ __('Create an account') }}</flux:link>
        </div>
    @endif

    <!-- Platform Features Highlight -->
    <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="text-center space-y-2">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                {{ __('What you can do with :platform', ['platform' => config('app.name', 'Graf')]) }}
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600 dark:text-gray-400">
                <div class="flex items-center justify-center space-x-1">
                    <span>📊</span>
                    <span>{{ __('YouTube Analytics') }}</span>
                </div>
                <div class="flex items-center justify-center space-x-1">
                    <span>🎥</span>
                    <span>{{ __('Video Insights') }}</span>
                </div>
                <div class="flex items-center justify-center space-x-1">
                    <span>📈</span>
                    <span>{{ __('Channel Growth') }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
