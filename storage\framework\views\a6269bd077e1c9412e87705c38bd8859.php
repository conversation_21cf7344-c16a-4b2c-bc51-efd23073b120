<?php
use <PERSON><PERSON><PERSON>\Youtube\Facades\Youtube;
use Al<PERSON>uy\Youtube\Rules\ValidYoutubeVideo;

$youtube_url = request('youtube_url', '');
$channel_input = request('channel_input', '');
$videoData = null;
$channelData = null;

if (request()->isMethod('post') && $youtube_url) {
    try {
        // Validate the URL
        $validator = validator(['youtube_url' => $youtube_url], [
            'youtube_url' => ['bail', 'required', 'string', new ValidYoutubeVideo]
        ]);

        if (!$validator->fails()) {
            // Extract video ID from URL
            $videoId = Youtube::parseVidFromURL($youtube_url);

            if (!$videoId) {
                throw new \Exception('Could not extract video ID from URL');
            }

            // Fetch video data
            $video = Youtube::getVideoInfo($videoId);

            dd($video);
        }
    } catch (\Exception $e) {
        throw new \Exception('Error fetching video data: ' . $e->getMessage());
    }
}

if (request()->isMethod('post') && $channel_input) {
    try {
        // Check if it is a handle url (/@...): if so use getChannelByHandle
        if (str_contains($channel_input, '/@')) {
            $handle = str_replace(['https://www.youtube.com/@', 'http://www.youtube.com/@'], '', $channel_input);
            $channel = Youtube::getChannelByHandle($handle);
        } else {
            // Else: use getChannelById
            $channel = Youtube::getChannelById($channel_input);
        }

        dd($channel);
    } catch (\Exception $e) {
        throw new \Exception('Error fetching channel data: ' . $e->getMessage());
    }
}
?>

<div class="max-w-2xl mx-auto p-6">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">YouTube Video Data Fetcher</h2>
        
        <form method="POST" action="<?php echo e(route('dashboard')); ?>" class="space-y-6">
            <?php echo csrf_field(); ?>

            <!-- Video URL Section -->
            <div>
                <label for="youtube_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    YouTube Video URL
                </label>
                <input
                    name="youtube_url"
                    id="youtube_url"
                    type="url"
                    placeholder="https://www.youtube.com/watch?v=..."
                    value="<?php echo e(old('youtube_url', $youtube_url)); ?>"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                <?php if(session('validation_errors') && in_array('youtube_url', array_keys(session('validation_errors', [])))): ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">
                        <?php echo e(session('validation_errors')['youtube_url'][0] ?? 'Invalid YouTube URL'); ?>

                    </p>
                <?php endif; ?>
                <div class="mt-2">
                    <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['type' => 'submit','variant' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'primary']); ?>
                        Fetch Video Data
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                </div>
            </div>

            <!-- Channel Input Section -->
            <div>
                <label for="channel_input" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    YouTube Channel (ID, URL, or Handle)
                </label>
                <input
                    name="channel_input"
                    id="channel_input"
                    type="text"
                    placeholder="UCk1SpWNzOs4MYmr0uICEntg or @google or https://www.youtube.com/@google"
                    value="<?php echo e(old('channel_input', $channel_input)); ?>"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                />
                <div class="mt-2">
                    <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['type' => 'submit','variant' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'primary']); ?>
                        Fetch Channel Data
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                </div>
            </div>

            <?php if($youtube_url || $channel_input): ?>
                <div>
                    <?php if (isset($component)) { $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'e60dd9d2c3a62d619c9acb38f20d5aa5::button.index','data' => ['type' => 'button','variant' => 'ghost','onclick' => 'window.location.href=\''.e(route('dashboard')).'\'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('flux::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'button','variant' => 'ghost','onclick' => 'window.location.href=\''.e(route('dashboard')).'\'']); ?>
                        Clear All
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $attributes = $__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__attributesOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580)): ?>
<?php $component = $__componentOriginalc04b147acd0e65cc1a77f86fb0e81580; ?>
<?php unset($__componentOriginalc04b147acd0e65cc1a77f86fb0e81580); ?>
<?php endif; ?>
                </div>
            <?php endif; ?>
        </form>


        <?php if($videoData): ?>
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Video Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The video data has been fetched and displayed in the debug output above.
                </p>
            </div>
        <?php endif; ?>

        <?php if($channelData): ?>
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Channel Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The channel data has been fetched and displayed in the debug output above.
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\graf\resources\views/components/youtube-form.blade.php ENDPATH**/ ?>