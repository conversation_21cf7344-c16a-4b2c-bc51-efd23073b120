<?php
use <PERSON><PERSON><PERSON>\Youtube\Facades\Youtube;
use <PERSON><PERSON>uy\Youtube\Rules\ValidYoutubeVideo;

$youtube_url = request('youtube_url', '');
$channel_input = request('channel_input', '');
$videoData = null;
$channelData = null;
$error = null;

if (request()->isMethod('post') && $youtube_url) {
    try {
        // Validate the URL
        $validator = validator(['youtube_url' => $youtube_url], [
            'youtube_url' => ['required', 'string', new ValidYoutubeVideo]
        ]);

        if ($validator->fails()) {
            $error = 'Invalid YouTube URL provided.';
            dd([
                'success' => false,
                'error' => 'Validation failed',
                'validation_errors' => $validator->errors()->all(),
                'url' => $youtube_url
            ]);
        } else {
            // Extract video ID from URL
            $videoId = Youtube::parseVidFromURL($youtube_url);

            if (!$videoId) {
                throw new \Exception('Could not extract video ID from URL');
            }

            // Fetch video data
            $video = Youtube::getVideoInfo($videoId);

            if (!$video) {
                throw new \Exception('Video not found or API error');
            }

            $videoData = $video;

            // Debug dump the response
            dd([
                'success' => true,
                'video_id' => $videoId,
                'video_data' => $video,
                'url' => $youtube_url
            ]);
        }
    } catch (\Exception $e) {
        $error = 'Error fetching video data: ' . $e->getMessage();
        dd([
            'success' => false,
            'error' => $e->getMessage(),
            'url' => $youtube_url,
            'trace' => $e->getTraceAsString()
        ]);
    }
}

if (request()->isMethod('post') && $channel_input) {
    try {
        $channel = null;

        // Determine if input is URL, handle, or ID and fetch accordingly
        if (str_starts_with($channel_input, 'https://www.youtube.com/@')) {
            // Handle format
            $handle = str_replace('https://www.youtube.com/@', '', $channel_input);
            $channel = Youtube::getChannelByHandle($handle);
        } elseif (str_starts_with($channel_input, '@')) {
            // Handle format without URL
            $handle = ltrim($channel_input, '@');
            $channel = Youtube::getChannelByHandle($handle);
        } elseif (str_starts_with($channel_input, 'UC') && strlen($channel_input) == 24) {
            // Channel ID format
            $channel = Youtube::getChannelById($channel_input);
        } elseif (str_contains($channel_input, 'youtube.com/channel/')) {
            // Channel URL format
            $channelId = str_replace('https://www.youtube.com/channel/', '', $channel_input);
            $channel = Youtube::getChannelById($channelId);
        } else {
            // Try as channel name
            $channel = Youtube::getChannelByName($channel_input);
        }

        if (!$channel) {
            throw new \Exception('Channel not found or API error');
        }

        $channelData = $channel;

        // Debug dump the response
        dd([
            'success' => true,
            'channel_data' => $channel,
            'input' => $channel_input
        ]);

    } catch (\Exception $e) {
        $error = 'Error fetching channel data: ' . $e->getMessage();
        dd([
            'success' => false,
            'error' => $e->getMessage(),
            'input' => $channel_input,
            'trace' => $e->getTraceAsString()
        ]);
    }
}
?>

<div class="max-w-2xl mx-auto p-6">
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">YouTube Video Data Fetcher</h2>
        
        <form method="POST" action="<?php echo e(route('dashboard')); ?>" class="space-y-6">
            <?php echo csrf_field(); ?>

            <!-- Video URL Section -->
            <div>
                <label for="youtube_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    YouTube Video URL
                </label>
                <input
                    name="youtube_url"
                    id="youtube_url"
                    type="url"
                    placeholder="https://www.youtube.com/watch?v=..."
                    value="<?php echo e(old('youtube_url', $youtube_url)); ?>"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
                <?php if(session('validation_errors') && in_array('youtube_url', array_keys(session('validation_errors', [])))): ?>
                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">
                        <?php echo e(session('validation_errors')['youtube_url'][0] ?? 'Invalid YouTube URL'); ?>

                    </p>
                <?php endif; ?>
                <div class="mt-2">
                    <button
                        type="submit"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                    >
                        Fetch Video Data
                    </button>
                </div>
            </div>

            <!-- Channel Input Section -->
            <div>
                <label for="channel_input" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    YouTube Channel (ID, URL, or Handle)
                </label>
                <input
                    name="channel_input"
                    id="channel_input"
                    type="text"
                    placeholder="UCk1SpWNzOs4MYmr0uICEntg or @google or https://www.youtube.com/@google"
                    value="<?php echo e(old('channel_input', $channel_input)); ?>"
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                />
                <div class="mt-2">
                    <button
                        type="submit"
                        class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
                    >
                        Fetch Channel Data
                    </button>
                </div>
            </div>

            <?php if($youtube_url || $channel_input): ?>
                <div>
                    <button
                        type="button"
                        onclick="window.location.href='<?php echo e(route('dashboard')); ?>'"
                        class="px-4 py-2 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                    >
                        Clear All
                    </button>
                </div>
            <?php endif; ?>
        </form>

        <?php if($error): ?>
            <div class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                        <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                            <?php echo e($error); ?>

                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if($videoData): ?>
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Video Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The video data has been fetched and displayed in the debug output above.
                </p>
            </div>
        <?php endif; ?>

        <?php if($channelData): ?>
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 class="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Channel Data Retrieved Successfully!</h3>
                <p class="text-sm text-green-700 dark:text-green-300">
                    The channel data has been fetched and displayed in the debug output above.
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\graf\resources\views/components/youtube-form.blade.php ENDPATH**/ ?>